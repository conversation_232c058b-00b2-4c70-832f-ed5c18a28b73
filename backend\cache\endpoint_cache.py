"""
Caching layer for API endpoints
Provides LRU caching with TTL for frequently accessed data
"""

import asyncio
import hashlib
import json
import time
from functools import wraps
from typing import Any, Dict, Optional, Callable, Tuple
from datetime import datetime, timedelta

from logger import logger


class TTLCache:
    """Time-To-Live cache with LRU eviction"""
    
    def __init__(self, maxsize: int = 128, ttl_seconds: int = 300):
        self.maxsize = maxsize
        self.ttl_seconds = ttl_seconds
        self.cache: Dict[str, Tuple[Any, float]] = {}
        self.access_order: Dict[str, float] = {}
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache if not expired"""
        async with self._lock:
            if key not in self.cache:
                return None
            
            value, timestamp = self.cache[key]
            
            # Check if expired
            if time.time() - timestamp > self.ttl_seconds:
                del self.cache[key]
                if key in self.access_order:
                    del self.access_order[key]
                return None
            
            # Update access time for LRU
            self.access_order[key] = time.time()
            return value
    
    async def set(self, key: str, value: Any) -> None:
        """Set value in cache with current timestamp"""
        async with self._lock:
            current_time = time.time()
            
            # If cache is full, remove LRU item
            if len(self.cache) >= self.maxsize and key not in self.cache:
                await self._evict_lru()
            
            self.cache[key] = (value, current_time)
            self.access_order[key] = current_time
    
    async def _evict_lru(self) -> None:
        """Remove least recently used item"""
        if not self.access_order:
            return
        
        lru_key = min(self.access_order.keys(), key=lambda k: self.access_order[k])
        del self.cache[lru_key]
        del self.access_order[lru_key]
    
    async def clear(self) -> None:
        """Clear all cache entries"""
        async with self._lock:
            self.cache.clear()
            self.access_order.clear()
    
    def stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            "size": len(self.cache),
            "maxsize": self.maxsize,
            "ttl_seconds": self.ttl_seconds,
            "hit_ratio": getattr(self, '_hit_count', 0) / max(getattr(self, '_total_count', 1), 1)
        }


# Global cache instances for different types of data
live_sessions_cache = TTLCache(maxsize=64, ttl_seconds=300)  # 5 minutes
live_session_cache = TTLCache(maxsize=128, ttl_seconds=180)  # 3 minutes
live_minute_cache = TTLCache(maxsize=64, ttl_seconds=120)    # 2 minutes
dahanghai_flow_cache = TTLCache(maxsize=32, ttl_seconds=600) # 10 minutes
dahanghai_period_cache = TTLCache(maxsize=16, ttl_seconds=900) # 15 minutes


def generate_cache_key(*args, **kwargs) -> str:
    """Generate a consistent cache key from arguments"""
    # Create a string representation of all arguments
    key_data = {
        'args': args,
        'kwargs': sorted(kwargs.items())
    }
    key_string = json.dumps(key_data, sort_keys=True, default=str)
    
    # Use hash for consistent, shorter keys
    return hashlib.md5(key_string.encode()).hexdigest()


def cached_async(cache_instance: TTLCache, key_prefix: str = ""):
    """Decorator for caching async function results"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = f"{key_prefix}:{generate_cache_key(*args, **kwargs)}"
            
            # Try to get from cache
            cached_result = await cache_instance.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {func.__name__}: {cache_key}")
                # Update hit statistics
                cache_instance._hit_count = getattr(cache_instance, '_hit_count', 0) + 1
                cache_instance._total_count = getattr(cache_instance, '_total_count', 0) + 1
                return cached_result
            
            # Cache miss - execute function
            logger.debug(f"Cache miss for {func.__name__}: {cache_key}")
            cache_instance._total_count = getattr(cache_instance, '_total_count', 0) + 1
            
            try:
                result = await func(*args, **kwargs)
                # Cache the result
                await cache_instance.set(cache_key, result)
                return result
            except Exception as e:
                logger.error(f"Error in cached function {func.__name__}: {e}")
                raise
        
        return wrapper
    return decorator


# Specific cache decorators for each endpoint type
def cache_live_sessions(func: Callable) -> Callable:
    """Cache decorator for live sessions queries"""
    return cached_async(live_sessions_cache, "live_sessions")(func)


def cache_live_session(func: Callable) -> Callable:
    """Cache decorator for single live session queries"""
    return cached_async(live_session_cache, "live_session")(func)


def cache_live_minute_data(func: Callable) -> Callable:
    """Cache decorator for live minute data queries"""
    return cached_async(live_minute_cache, "live_minute")(func)


def cache_dahanghai_flow(func: Callable) -> Callable:
    """Cache decorator for dahanghai flow analysis"""
    return cached_async(dahanghai_flow_cache, "dahanghai_flow")(func)


def cache_dahanghai_period(func: Callable) -> Callable:
    """Cache decorator for dahanghai period analysis"""
    return cached_async(dahanghai_period_cache, "dahanghai_period")(func)


async def clear_all_caches():
    """Clear all endpoint caches"""
    await asyncio.gather(
        live_sessions_cache.clear(),
        live_session_cache.clear(),
        live_minute_cache.clear(),
        dahanghai_flow_cache.clear(),
        dahanghai_period_cache.clear()
    )
    logger.info("All endpoint caches cleared")


async def get_cache_stats() -> Dict[str, Any]:
    """Get statistics for all caches"""
    return {
        "live_sessions": live_sessions_cache.stats(),
        "live_session": live_session_cache.stats(),
        "live_minute": live_minute_cache.stats(),
        "dahanghai_flow": dahanghai_flow_cache.stats(),
        "dahanghai_period": dahanghai_period_cache.stats()
    }


# Cache warming functions
async def warm_live_sessions_cache(room_ids: list):
    """Pre-warm live sessions cache for common room IDs"""
    from backend.query.query_live_info_optimized import query_live_info_with_room_id_optimized
    
    logger.info(f"Warming live sessions cache for {len(room_ids)} rooms")
    
    tasks = []
    for room_id in room_ids:
        # Create cached version of the function
        cached_func = cache_live_sessions(query_live_info_with_room_id_optimized)
        tasks.append(cached_func(room_id))
    
    try:
        await asyncio.gather(*tasks, return_exceptions=True)
        logger.info("Live sessions cache warming completed")
    except Exception as e:
        logger.error(f"Error warming live sessions cache: {e}")


async def warm_dahanghai_flow_cache(targets: list, date_pairs: list):
    """Pre-warm dahanghai flow cache for common targets and date pairs"""
    from backend.query.query_board_data_optimized import collect_target_flow_with_target_vtuber_optimized
    
    logger.info(f"Warming dahanghai flow cache for {len(targets)} targets and {len(date_pairs)} date pairs")
    
    tasks = []
    for target in targets:
        for current_date, previous_date in date_pairs:
            # Create cached version of the function
            cached_func = cache_dahanghai_flow(collect_target_flow_with_target_vtuber_optimized)
            tasks.append(cached_func(target, current_date, previous_date, 5))
    
    try:
        await asyncio.gather(*tasks, return_exceptions=True)
        logger.info("Dahanghai flow cache warming completed")
    except Exception as e:
        logger.error(f"Error warming dahanghai flow cache: {e}")


# Cache management endpoint for monitoring
async def get_cache_health() -> Dict[str, Any]:
    """Get comprehensive cache health information"""
    stats = await get_cache_stats()
    
    # Calculate overall health metrics
    total_size = sum(cache_stats["size"] for cache_stats in stats.values())
    total_maxsize = sum(cache_stats["maxsize"] for cache_stats in stats.values())
    avg_hit_ratio = sum(cache_stats["hit_ratio"] for cache_stats in stats.values()) / len(stats)
    
    return {
        "individual_caches": stats,
        "overall": {
            "total_entries": total_size,
            "total_capacity": total_maxsize,
            "capacity_utilization": total_size / total_maxsize if total_maxsize > 0 else 0,
            "average_hit_ratio": avg_hit_ratio,
            "health_status": "good" if avg_hit_ratio > 0.7 else "fair" if avg_hit_ratio > 0.4 else "poor"
        },
        "timestamp": datetime.now().isoformat()
    }
