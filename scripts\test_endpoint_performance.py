#!/usr/bin/env python3
"""
Performance testing script for the 5 optimized API endpoints
Tests functionality and measures response times before and after optimization
"""

import asyncio
import aiohttp
import time
import json
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Any
import statistics

# Test configuration
BASE_URL = "http://localhost:9022"
TEST_ITERATIONS = 5
TIMEOUT = 30  # seconds

# Test data
TEST_VTUBER = "星瞳"
TEST_ROOM_ID = "22886883"
TEST_LIVE_ID = None  # Will be fetched from database
TEST_DATES = {
    "current": "2025-08-20",
    "previous": "2025-08-15",
    "start1": "2025-08-01",
    "end1": "2025-08-10",
    "start2": "2025-08-11", 
    "end2": "2025-08-20"
}

# Endpoints to test
ENDPOINTS = [
    {
        "name": "Live Sessions",
        "url": f"/live/sessions?vtuber={TEST_VTUBER}",
        "method": "GET"
    },
    {
        "name": "Live One Session",
        "url": f"/live/one_session?liveId={TEST_LIVE_ID}",
        "method": "GET"
    },
    {
        "name": "Live One Session Minute",
        "url": f"/live/one_session/minute?liveId={TEST_LIVE_ID}&interval=10",
        "method": "GET"
    },
    {
        "name": "Board Dahanghai Target",
        "url": f"/board/dahanghai/target?target={TEST_VTUBER}&c={TEST_DATES['current']}&p={TEST_DATES['previous']}&n=5",
        "method": "GET"
    },
    {
        "name": "Board Dahanghai Target Period",
        "url": f"/board/dahanghai/target_period?target={TEST_VTUBER}&start_time_str1={TEST_DATES['start1']}&end_time_str1={TEST_DATES['end1']}&start_time_str2={TEST_DATES['start2']}&end_time_str2={TEST_DATES['end2']}&n=5",
        "method": "GET"
    }
]


class PerformanceTester:
    def __init__(self):
        self.results = {}
        self.session = None
        self.live_id = None
    
    async def __aenter__(self):
        timeout = aiohttp.ClientTimeout(total=TIMEOUT)
        self.session = aiohttp.ClientSession(timeout=timeout)

        # Get a real live_id from the database
        await self._get_test_live_id()

        return self

    async def _get_test_live_id(self):
        """Get a real live_id for testing"""
        try:
            # Try to get live sessions first
            async with self.session.get(f"{BASE_URL}/live/sessions?vtuber={TEST_VTUBER}") as response:
                if response.status == 200:
                    data = await response.json()
                    sessions = data.get('data', {}).get('sessions', [])
                    if sessions:
                        self.live_id = sessions[0].get('liveId')
                        print(f"Using live_id for testing: {self.live_id}")
                        return

            # Fallback to a default test ID
            self.live_id = "test_live_id"
            print(f"Using fallback live_id: {self.live_id}")

        except Exception as e:
            print(f"Could not get live_id, using fallback: {e}")
            self.live_id = "test_live_id"
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_endpoint(self, endpoint: Dict[str, str]) -> Dict[str, Any]:
        """Test a single endpoint and measure performance"""
        print(f"\nTesting {endpoint['name']}...")
        print(f"URL: {endpoint['url']}")
        
        response_times = []
        success_count = 0
        errors = []
        
        for i in range(TEST_ITERATIONS):
            try:
                start_time = time.time()
                
                async with self.session.request(
                    endpoint['method'], 
                    f"{BASE_URL}{endpoint['url']}"
                ) as response:
                    end_time = time.time()
                    response_time = (end_time - start_time) * 1000  # Convert to milliseconds
                    
                    if response.status == 200:
                        success_count += 1
                        response_times.append(response_time)
                        
                        # Get response data for validation
                        response_data = await response.json()
                        
                        print(f"  Iteration {i+1}: {response_time:.2f}ms - SUCCESS")
                        
                        # Validate response structure
                        if not self._validate_response(response_data, endpoint['name']):
                            errors.append(f"Invalid response structure in iteration {i+1}")
                    else:
                        error_text = await response.text()
                        error_msg = f"HTTP {response.status}: {error_text[:100]}"
                        errors.append(error_msg)
                        print(f"  Iteration {i+1}: FAILED - {error_msg}")
                        
            except asyncio.TimeoutError:
                error_msg = f"Timeout after {TIMEOUT}s"
                errors.append(error_msg)
                print(f"  Iteration {i+1}: FAILED - {error_msg}")
                
            except Exception as e:
                error_msg = f"Error: {str(e)}"
                errors.append(error_msg)
                print(f"  Iteration {i+1}: FAILED - {error_msg}")
        
        # Calculate statistics
        if response_times:
            avg_time = statistics.mean(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            median_time = statistics.median(response_times)
            
            if len(response_times) > 1:
                std_dev = statistics.stdev(response_times)
            else:
                std_dev = 0
        else:
            avg_time = min_time = max_time = median_time = std_dev = 0
        
        result = {
            "endpoint": endpoint['name'],
            "url": endpoint['url'],
            "total_requests": TEST_ITERATIONS,
            "successful_requests": success_count,
            "success_rate": (success_count / TEST_ITERATIONS) * 100,
            "response_times_ms": response_times,
            "avg_response_time_ms": avg_time,
            "min_response_time_ms": min_time,
            "max_response_time_ms": max_time,
            "median_response_time_ms": median_time,
            "std_dev_ms": std_dev,
            "errors": errors
        }
        
        print(f"  Results: {success_count}/{TEST_ITERATIONS} successful")
        if response_times:
            print(f"  Average response time: {avg_time:.2f}ms")
            print(f"  Min/Max: {min_time:.2f}ms / {max_time:.2f}ms")
        
        return result
    
    def _validate_response(self, response_data: Dict, endpoint_name: str) -> bool:
        """Validate response structure"""
        try:
            # Check basic response structure
            if not isinstance(response_data, dict):
                return False
            
            if "code" not in response_data or "message" not in response_data:
                return False
            
            if response_data["code"] != 200:
                return False
            
            # Check data field exists
            if "data" not in response_data:
                return False
            
            data = response_data["data"]
            
            # Endpoint-specific validation
            if "Live Sessions" in endpoint_name:
                return "sessions" in data and isinstance(data["sessions"], list)
            elif "Live One Session" in endpoint_name:
                return "result" in data and isinstance(data["result"], dict)
            elif "Board Dahanghai" in endpoint_name:
                return "result" in data and isinstance(data["result"], dict)
            
            return True
            
        except Exception:
            return False
    
    async def test_cache_performance(self):
        """Test cache performance by running the same request multiple times"""
        print("\n" + "="*60)
        print("TESTING CACHE PERFORMANCE")
        print("="*60)
        
        # Test first endpoint multiple times to see cache effect
        endpoint = ENDPOINTS[0]  # Live Sessions
        
        print(f"Testing cache performance for {endpoint['name']}")
        print("First request (cache miss):")
        
        # First request (cache miss)
        start_time = time.time()
        async with self.session.get(f"{BASE_URL}{endpoint['url']}") as response:
            first_time = (time.time() - start_time) * 1000
            print(f"  Response time: {first_time:.2f}ms")
        
        # Subsequent requests (cache hits)
        print("Subsequent requests (cache hits):")
        cache_times = []
        
        for i in range(3):
            start_time = time.time()
            async with self.session.get(f"{BASE_URL}{endpoint['url']}") as response:
                cache_time = (time.time() - start_time) * 1000
                cache_times.append(cache_time)
                print(f"  Request {i+1}: {cache_time:.2f}ms")
        
        avg_cache_time = statistics.mean(cache_times)
        improvement = ((first_time - avg_cache_time) / first_time) * 100
        
        print(f"\nCache Performance Summary:")
        print(f"  First request (cache miss): {first_time:.2f}ms")
        print(f"  Average cached request: {avg_cache_time:.2f}ms")
        print(f"  Performance improvement: {improvement:.1f}%")
        
        return {
            "cache_miss_time_ms": first_time,
            "cache_hit_avg_time_ms": avg_cache_time,
            "improvement_percentage": improvement
        }
    
    async def get_cache_stats(self):
        """Get cache statistics from the server"""
        try:
            async with self.session.get(f"{BASE_URL}/admin/cache/stats") as response:
                if response.status == 200:
                    return await response.json()
        except Exception as e:
            print(f"Could not get cache stats: {e}")
        return None
    
    async def run_all_tests(self):
        """Run all performance tests"""
        print("="*60)
        print("API ENDPOINT PERFORMANCE TESTING")
        print("="*60)
        print(f"Base URL: {BASE_URL}")
        print(f"Test iterations per endpoint: {TEST_ITERATIONS}")
        print(f"Timeout: {TIMEOUT}s")
        print(f"Using live_id: {self.live_id}")

        # Update endpoints with real live_id
        updated_endpoints = []
        for endpoint in ENDPOINTS:
            updated_endpoint = endpoint.copy()
            if self.live_id and "liveId=" in updated_endpoint['url']:
                updated_endpoint['url'] = updated_endpoint['url'].replace("test_live_id", self.live_id)
            updated_endpoints.append(updated_endpoint)

        # Test each endpoint
        for endpoint in updated_endpoints:
            result = await self.test_endpoint(endpoint)
            self.results[endpoint['name']] = result
        
        # Test cache performance
        cache_result = await self.test_cache_performance()
        self.results['cache_performance'] = cache_result
        
        # Get cache statistics
        cache_stats = await self.get_cache_stats()
        if cache_stats:
            self.results['cache_stats'] = cache_stats
        
        # Print summary
        self.print_summary()
        
        return self.results
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "="*60)
        print("PERFORMANCE TEST SUMMARY")
        print("="*60)
        
        total_endpoints = len(ENDPOINTS)
        successful_endpoints = 0
        total_avg_time = 0
        
        for endpoint_name, result in self.results.items():
            if endpoint_name in ['cache_performance', 'cache_stats']:
                continue
                
            success_rate = result['success_rate']
            avg_time = result['avg_response_time_ms']
            
            status = "✓ PASS" if success_rate == 100 else "✗ FAIL"
            print(f"{endpoint_name:30} {status:8} {avg_time:8.2f}ms ({success_rate:5.1f}%)")
            
            if success_rate == 100:
                successful_endpoints += 1
                total_avg_time += avg_time
        
        print("-" * 60)
        print(f"Endpoints passed: {successful_endpoints}/{total_endpoints}")
        
        if successful_endpoints > 0:
            avg_response_time = total_avg_time / successful_endpoints
            print(f"Average response time: {avg_response_time:.2f}ms")
        
        # Cache performance summary
        if 'cache_performance' in self.results:
            cache_perf = self.results['cache_performance']
            print(f"Cache improvement: {cache_perf['improvement_percentage']:.1f}%")


async def main():
    """Main function"""
    try:
        async with PerformanceTester() as tester:
            results = await tester.run_all_tests()
            
            # Save results to file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = f"performance_test_results_{timestamp}.json"
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            print(f"\nDetailed results saved to: {results_file}")
            
            # Return exit code based on success
            total_endpoints = len(ENDPOINTS)
            successful_endpoints = sum(
                1 for name, result in results.items() 
                if name not in ['cache_performance', 'cache_stats'] and result['success_rate'] == 100
            )
            
            return 0 if successful_endpoints == total_endpoints else 1
            
    except Exception as e:
        print(f"Error running performance tests: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
