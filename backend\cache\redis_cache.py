"""
Redis caching module for performance optimization
"""

import json
import time
import redis
from datetime import timed<PERSON>ta
from functools import wraps
from typing import Optional, Any, Union
from logger import logger

# Redis client configuration
REDIS_CONFIG = {
    "host": "localhost",
    "port": 6379,
    "db": 0,
    "decode_responses": True,
    "socket_connect_timeout": 5,
    "socket_timeout": 5,
    "retry_on_timeout": True,
    "health_check_interval": 30
}

# Global Redis client
_redis_client: Optional[redis.Redis] = None

def get_redis_client() -> Optional[redis.Redis]:
    """Get Redis client instance with connection handling"""
    global _redis_client
    
    if _redis_client is None:
        try:
            _redis_client = redis.Redis(**REDIS_CONFIG)
            # Test connection
            _redis_client.ping()
            logger.info("Redis client connected successfully")
        except redis.ConnectionError:
            logger.warning("Redis server not available, caching disabled")
            _redis_client = None
        except Exception as e:
            logger.error(f"Failed to initialize Redis client: {e}")
            _redis_client = None
    
    return _redis_client

def is_redis_available() -> bool:
    """Check if Redis is available"""
    client = get_redis_client()
    if client is None:
        return False
    
    try:
        client.ping()
        return True
    except Exception:
        return False

class CacheManager:
    """Redis cache manager with fallback to in-memory cache"""
    
    def __init__(self):
        self.redis_client = get_redis_client()
        self._memory_cache = {}  # Fallback in-memory cache
        self._memory_cache_ttl = {}  # TTL tracking for memory cache
    
    def _clean_memory_cache(self):
        """Clean expired entries from memory cache"""
        current_time = time.time()
        expired_keys = [
            key for key, ttl in self._memory_cache_ttl.items()
            if current_time > ttl
        ]
        
        for key in expired_keys:
            self._memory_cache.pop(key, None)
            self._memory_cache_ttl.pop(key, None)
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache (Redis first, then memory fallback)"""
        # Try Redis first
        if self.redis_client:
            try:
                value = self.redis_client.get(key)
                if value is not None:
                    logger.debug(f"Cache hit (Redis): {key}")
                    return json.loads(value)
            except Exception as e:
                logger.warning(f"Redis get failed for key {key}: {e}")
        
        # Fallback to memory cache
        self._clean_memory_cache()
        if key in self._memory_cache:
            logger.debug(f"Cache hit (Memory): {key}")
            return self._memory_cache[key]
        
        logger.debug(f"Cache miss: {key}")
        return None
    
    def set(self, key: str, value: Any, ttl_seconds: int = 300) -> bool:
        """Set value in cache (Redis first, then memory fallback)"""
        serialized_value = json.dumps(value, default=str)
        
        # Try Redis first
        if self.redis_client:
            try:
                result = self.redis_client.setex(key, ttl_seconds, serialized_value)
                if result:
                    logger.debug(f"Cache set (Redis): {key}, TTL: {ttl_seconds}s")
                    return True
            except Exception as e:
                logger.warning(f"Redis set failed for key {key}: {e}")
        
        # Fallback to memory cache
        self._memory_cache[key] = value
        self._memory_cache_ttl[key] = time.time() + ttl_seconds
        logger.debug(f"Cache set (Memory): {key}, TTL: {ttl_seconds}s")
        return True
    
    def delete(self, key: str) -> bool:
        """Delete value from cache"""
        success = False
        
        # Delete from Redis
        if self.redis_client:
            try:
                result = self.redis_client.delete(key)
                success = bool(result)
            except Exception as e:
                logger.warning(f"Redis delete failed for key {key}: {e}")
        
        # Delete from memory cache
        if key in self._memory_cache:
            del self._memory_cache[key]
            self._memory_cache_ttl.pop(key, None)
            success = True
        
        if success:
            logger.debug(f"Cache deleted: {key}")
        
        return success
    
    def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern"""
        deleted_count = 0
        
        # Clear from Redis
        if self.redis_client:
            try:
                keys = self.redis_client.keys(pattern)
                if keys:
                    deleted_count += self.redis_client.delete(*keys)
            except Exception as e:
                logger.warning(f"Redis pattern clear failed for {pattern}: {e}")
        
        # Clear from memory cache
        memory_keys = [key for key in self._memory_cache.keys() if pattern.replace('*', '') in key]
        for key in memory_keys:
            self._memory_cache.pop(key, None)
            self._memory_cache_ttl.pop(key, None)
            deleted_count += 1
        
        logger.info(f"Cleared {deleted_count} cache entries matching pattern: {pattern}")
        return deleted_count

# Global cache manager instance
cache_manager = CacheManager()

def cache_result(key_prefix: str = "", ttl_minutes: int = 5, use_args: bool = True):
    """
    Decorator for caching function results
    
    Args:
        key_prefix: Prefix for cache key
        ttl_minutes: Time to live in minutes
        use_args: Whether to include function arguments in cache key
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            if use_args:
                # Create key from function name, args, and kwargs
                args_str = "_".join(str(arg) for arg in args)
                kwargs_str = "_".join(f"{k}={v}" for k, v in sorted(kwargs.items()))
                cache_key = f"{key_prefix}:{func.__name__}:{args_str}:{kwargs_str}"
            else:
                cache_key = f"{key_prefix}:{func.__name__}"
            
            # Clean cache key (remove invalid characters)
            cache_key = cache_key.replace(" ", "_").replace(":", "_")
            
            # Try to get from cache
            start_time = time.time()
            cached_result = cache_manager.get(cache_key)
            
            if cached_result is not None:
                cache_time = time.time() - start_time
                logger.info(f"Cache hit for {func.__name__} in {cache_time:.3f}s")
                return cached_result
            
            # Execute function
            logger.debug(f"Cache miss for {func.__name__}, executing function")
            result = await func(*args, **kwargs)
            
            # Cache the result
            if result is not None:
                cache_manager.set(cache_key, result, ttl_minutes * 60)
                logger.debug(f"Cached result for {func.__name__} with TTL {ttl_minutes}m")
            
            return result
        
        return wrapper
    return decorator

# Convenience decorators for common use cases
def cache_live_status(ttl_minutes: int = 5):
    """Cache decorator specifically for live status data"""
    return cache_result(key_prefix="live_status", ttl_minutes=ttl_minutes, use_args=False)

def cache_user_data(ttl_minutes: int = 30):
    """Cache decorator for user data"""
    return cache_result(key_prefix="user_data", ttl_minutes=ttl_minutes, use_args=True)

def cache_board_data(ttl_minutes: int = 10):
    """Cache decorator for board data"""
    return cache_result(key_prefix="board_data", ttl_minutes=ttl_minutes, use_args=True)
