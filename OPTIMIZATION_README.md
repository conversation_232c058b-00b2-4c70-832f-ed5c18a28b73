# API Endpoint Performance Optimization

This document describes the performance optimizations implemented for 5 specific API endpoints in the backend application.

## Optimized Endpoints

1. **`/live/sessions`** - Get all live streaming sessions for a VTuber
2. **`/live/one_session`** - Get complete information for a specific live session
3. **`/live/one_session/minute`** - Get live session with minute-by-minute data
4. **`/board/dahanghai/target`** - Get dahanghai flow analysis for a specific VTuber
5. **`/board/dahanghai/target_period`** - Get dahanghai flow analysis between periods

## Optimization Techniques Applied

### 1. Database Query Optimization
- **Combined Queries**: Reduced N+1 query problems by using JOINs and subqueries
- **Optimized SQL**: Replaced multiple database round trips with single comprehensive queries
- **Database-level Operations**: Moved set operations from Python to SQL for better performance

### 2. Performance Indexes
- **Composite Indexes**: Created specific indexes for query patterns used by endpoints
- **Covering Indexes**: Added indexes that include commonly selected columns
- **Partial Indexes**: Created indexes for recent data that's accessed most frequently

### 3. Caching Layer
- **LRU Cache with TTL**: Implemented time-based caching for frequently accessed data
- **Endpoint-specific Caching**: Different cache configurations for different data types
- **Cache Management**: Added endpoints for monitoring and clearing caches

### 4. Code Optimizations
- **Optimized Query Functions**: Created new versions of query functions with better performance
- **Reduced Memory Usage**: Minimized data processing in Python by doing more work in database
- **Error Handling**: Improved error handling and logging for better debugging

## Files Created/Modified

### New Files
- `backend/query/query_live_info_optimized.py` - Optimized live info query functions
- `backend/query/query_board_data_optimized.py` - Optimized board data query functions
- `backend/cache/endpoint_cache.py` - Caching layer implementation
- `sql/endpoint_performance_indexes.sql` - Database performance indexes
- `scripts/apply_endpoint_indexes.py` - Script to apply database indexes
- `scripts/test_endpoint_performance.py` - Performance testing script
- `scripts/setup_and_test_optimizations.py` - Complete setup and testing script

### Modified Files
- `backend/app.py` - Updated endpoints to use optimized functions and caching

## Setup and Testing

### Prerequisites
1. Backend server running on `http://localhost:9022`
2. Database connection configured
3. Conda environment `clct` activated

### Quick Setup
Run the comprehensive setup script:
```bash
conda activate clct
cd /path/to/project
python scripts/setup_and_test_optimizations.py
```

This script will:
1. Check server status
2. Apply database performance indexes
3. Run performance tests
4. Generate optimization report

### Manual Setup

#### 1. Apply Database Indexes
```bash
python scripts/apply_endpoint_indexes.py
```

#### 2. Test Performance
```bash
python scripts/test_endpoint_performance.py
```

#### 3. Monitor Cache Performance
```bash
# Get cache statistics
curl http://localhost:9022/admin/cache/stats

# Clear all caches
curl -X POST http://localhost:9022/admin/cache/clear
```

## Performance Improvements

### Expected Improvements
- **Database Queries**: 50-80% reduction in query time due to optimized SQL and indexes
- **Memory Usage**: 30-60% reduction by moving operations to database level
- **Response Times**: 40-70% improvement for cached responses
- **Scalability**: Better performance under high load due to reduced database pressure

### Cache Performance
- **Cache Hit Ratio**: Target >70% for frequently accessed endpoints
- **Cache TTL**: 2-15 minutes depending on data type
- **Memory Usage**: Configurable cache sizes with LRU eviction

## Monitoring and Maintenance

### Cache Monitoring
- Check cache statistics: `GET /admin/cache/stats`
- Monitor hit ratios and adjust TTL if needed
- Clear caches during deployments: `POST /admin/cache/clear`

### Database Monitoring
- Monitor index usage with `pg_stat_user_indexes`
- Check query performance with `EXPLAIN ANALYZE`
- Monitor table and index sizes

### Performance Monitoring
- Use the performance middleware to track slow requests
- Monitor response times in production
- Set up alerts for response times >1 second

## Configuration

### Cache Configuration
Cache settings can be adjusted in `backend/cache/endpoint_cache.py`:
- `maxsize`: Maximum number of cached items
- `ttl_seconds`: Time-to-live for cached items

### Database Indexes
Indexes are defined in `sql/endpoint_performance_indexes.sql` and can be customized based on query patterns.

## Troubleshooting

### Common Issues

1. **Cache Not Working**
   - Check if caching decorators are applied correctly
   - Verify cache statistics show hit/miss counts
   - Clear cache and test again

2. **Slow Queries**
   - Check if indexes are created: `\d+ table_name` in psql
   - Use `EXPLAIN ANALYZE` to check query plans
   - Monitor database logs for slow queries

3. **High Memory Usage**
   - Reduce cache sizes if needed
   - Monitor cache statistics for memory usage
   - Consider shorter TTL for less frequently accessed data

### Performance Testing
Run performance tests regularly to ensure optimizations are working:
```bash
python scripts/test_endpoint_performance.py
```

## Future Optimizations

### Potential Improvements
1. **Connection Pooling**: Optimize database connection pool settings
2. **Query Caching**: Implement query result caching at database level
3. **Data Partitioning**: Partition large tables by date for better performance
4. **Read Replicas**: Use read replicas for read-heavy endpoints
5. **CDN Caching**: Add CDN caching for static or semi-static data

### Monitoring Recommendations
1. Set up APM (Application Performance Monitoring)
2. Monitor database performance metrics
3. Track cache hit ratios and response times
4. Set up alerts for performance degradation

## Contact

For questions about these optimizations or performance issues, please check the logs and performance test results first, then consult the development team.
