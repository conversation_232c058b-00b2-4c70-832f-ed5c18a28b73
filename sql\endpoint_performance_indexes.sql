-- Performance optimization indexes for the 5 target API endpoints
-- These indexes will significantly improve query performance

-- ============================================================================
-- INDEXES FOR /live/sessions ENDPOINT
-- ============================================================================

-- Primary index for live_session_table: room_id and datetime (most critical)
-- This supports the main query filtering by room_id and ordering by datetime
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_session_room_datetime 
ON live_session_table(room_id, datetime DESC, start_time_str DESC);

-- Covering index for live_session_table to avoid table lookups
-- Includes commonly selected columns for the optimized query
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_session_covering 
ON live_session_table(room_id, datetime DESC) 
INCLUDE (live_id, danmu_count, start_time_str, end_time_str, income, watch_change_count, 
         ave_online_rank, ave_enter_room, enter_room_count, parent_area, area, cover, title);

-- Index for live_status_minute_table: live_id and live_action
-- This supports the EXISTS queries for is_finish and is_full flags
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_status_live_id_action 
ON live_status_minute_table(live_id, live_action);

-- ============================================================================
-- INDEXES FOR /live/one_session AND /live/one_session/minute ENDPOINTS
-- ============================================================================

-- Primary index for live_session_table: live_id (for single session queries)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_session_live_id 
ON live_session_table(live_id);

-- Index for live_status_minute_table: live_id and timestamp (for minute data)
-- This supports the minute-level data queries with proper ordering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_status_live_id_timestamp 
ON live_status_minute_table(live_id, timestamp);

-- Covering index for minute data queries to avoid table lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_status_minute_covering 
ON live_status_minute_table(live_id, timestamp) 
INCLUDE (danmu_count, active_watcher_count, online_rank_count, income_count, 
         interact_count, enter_count);

-- ============================================================================
-- INDEXES FOR /board/dahanghai/target AND /board/dahanghai/target_period ENDPOINTS
-- ============================================================================

-- Primary composite index for dahanghai_list_table: time and up_name
-- This is the most critical index for flow analysis queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_dahanghai_time_up_name 
ON dahanghai_list_table(time, up_name);

-- Index for dahanghai_list_table: up_name and time (reverse order)
-- This supports queries that filter by up_name first
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_dahanghai_up_name_time 
ON dahanghai_list_table(up_name, time);

-- Index for dahanghai_list_table: uid (for JOIN operations)
-- This supports the user flow analysis JOINs
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_dahanghai_uid 
ON dahanghai_list_table(uid);

-- Composite index for dahanghai_list_table: time, up_name, uid
-- This is a covering index for the main flow analysis queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_dahanghai_time_up_name_uid 
ON dahanghai_list_table(time, up_name, uid) 
INCLUDE (username, face);

-- Index for date range queries (for target_period endpoint)
-- This supports efficient date range filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_dahanghai_date_range 
ON dahanghai_list_table(time::date, up_name);

-- Partial index for recent data (last 90 days)
-- This optimizes queries for recent flow analysis which are most common
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_dahanghai_recent 
ON dahanghai_list_table(time, up_name, uid) 
WHERE time >= CURRENT_DATE - INTERVAL '90 DAYS';

-- ============================================================================
-- ADDITIONAL PERFORMANCE INDEXES
-- ============================================================================

-- Index for room_id lookups in live_status_minute_table
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_status_room_id 
ON live_status_minute_table(room_id);

-- Partial index for active live sessions (recent data)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_session_recent 
ON live_session_table(room_id, datetime DESC) 
WHERE datetime >= CURRENT_DATE - INTERVAL '30 DAYS';

-- Index for live_status_minute_table: timestamp only (for global queries)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_status_timestamp 
ON live_status_minute_table(timestamp DESC);

-- ============================================================================
-- MAINTENANCE QUERIES
-- ============================================================================

-- Query to check index usage (run after creating indexes)
-- SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch 
-- FROM pg_stat_user_indexes 
-- WHERE indexname LIKE 'idx_%' 
-- ORDER BY idx_scan DESC;

-- Query to check table sizes and index sizes
-- SELECT 
--     schemaname,
--     tablename,
--     pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as table_size,
--     pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as indexes_size
-- FROM pg_tables 
-- WHERE tablename IN ('live_session_table', 'live_status_minute_table', 'dahanghai_list_table')
-- ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
