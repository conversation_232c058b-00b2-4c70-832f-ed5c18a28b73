import jieba
from wordcloud import WordCloud
import numpy as np
from PIL import Image
import os
import logging
import time
from const import PROJECT_ROOT
from utils import get_zh_role_name

logger = logging.getLogger(__name__)

# Load stopwords at module level with error handling
STOPWORDS_ZH = set()
try:
    stopwords_path = f"{PROJECT_ROOT}/utils/stop_words_zh.txt"
    if os.path.exists(stopwords_path):
        with open(stopwords_path, encoding="utf-8") as f:
            STOPWORDS_ZH = set(map(str.strip, f.readlines()))
        logger.info(f"Loaded {len(STOPWORDS_ZH)} Chinese stopwords")
    else:
        logger.warning(f"Stopwords file not found: {stopwords_path}")
except Exception as e:
    logger.error(f"Failed to load Chinese stopwords: {e}")


def word_cloud_gen(context_list: list, target_path: str, char_zh_name: str):
    """
    Generate word cloud image from text content.

    Args:
        context_list: List of text strings to generate word cloud from
        target_path: Output file path for the word cloud image
        char_zh_name: Chinese character name (used directly, not processed through get_zh_role_name)

    Raises:
        FileNotFoundError: If required resource files are missing
        PermissionError: If file access is denied
        Exception: For other generation errors
    """
    try:
        # Validate input parameters
        if not context_list:
            raise ValueError("Context list cannot be empty")
        if not target_path:
            raise ValueError("Target path cannot be empty")
        if not char_zh_name:
            raise ValueError("Character name cannot be empty")

        # Construct file paths
        image_path = f"{PROJECT_ROOT}/data/images/vtubers/{char_zh_name}.jpg"
        font_path = f"{PROJECT_ROOT}/utils/fonts/msyh.ttf"

        # Validate required files exist
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Vtuber image file not found: {image_path}")
        if not os.path.exists(font_path):
            raise FileNotFoundError(f"Font file not found: {font_path}")

        # Load mask image with retry logic for concurrent access
        mask_image = None
        max_retries = 3
        for attempt in range(max_retries):
            try:
                with Image.open(image_path) as img:
                    mask_image = np.array(img.copy())
                break
            except (OSError, PermissionError) as e:
                if attempt < max_retries - 1:
                    logger.warning(f"Failed to open image file (attempt {attempt + 1}/{max_retries}): {e}")
                    time.sleep(0.1 * (attempt + 1))  # Exponential backoff
                    continue
                else:
                    raise FileNotFoundError(f"Unable to open vtuber image after {max_retries} attempts: {image_path}") from e

        if mask_image is None:
            raise RuntimeError("Failed to load mask image")

        # Process text content
        context = " ".join(str(text) for text in context_list if text)
        if not context.strip():
            raise ValueError("No valid text content to generate word cloud")

        words = jieba.lcut(context)
        newtxt = " ".join(words)

        if not newtxt.strip():
            raise ValueError("No valid words after text processing")

        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(target_path)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        # Generate word cloud with error handling
        try:
            wordcloud = WordCloud(
                width=800,
                height=800,
                background_color="white",
                mask=mask_image,
                stopwords=STOPWORDS_ZH,
                max_words=500,
                min_font_size=2,
                contour_width=0.5,
                font_path=font_path,
                contour_color="yellow",
            ).generate(newtxt)

            # Save with retry logic for file system issues
            for attempt in range(max_retries):
                try:
                    wordcloud.to_file(target_path)
                    # Verify file was created successfully
                    if os.path.exists(target_path) and os.path.getsize(target_path) > 0:
                        logger.info(f"Word cloud generated successfully: {target_path}")
                        return
                    else:
                        raise RuntimeError("Word cloud file was not created or is empty")
                except (OSError, PermissionError) as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"Failed to save word cloud (attempt {attempt + 1}/{max_retries}): {e}")
                        time.sleep(0.1 * (attempt + 1))
                        continue
                    else:
                        raise PermissionError(f"Unable to save word cloud after {max_retries} attempts: {target_path}") from e

        except Exception as e:
            raise RuntimeError(f"Word cloud generation failed: {e}") from e

    except Exception as e:
        logger.error(f"Word cloud generation error for {char_zh_name}: {e}")
        raise
