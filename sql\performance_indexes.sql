-- Performance optimization indexes for live status queries
-- These indexes will significantly improve query performance for the read_all_live_status endpoint

-- Index for live_status_minute_table: room_id and datetime (most critical)
-- This supports the DISTINCT ON query and datetime filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_status_room_datetime 
ON live_status_minute_table(room_id, datetime DESC);

-- Index for user_info_table: room_id for JOIN operations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_info_room_id 
ON user_info_table(room_id);

-- Partial index for recent live status data (last 7 days)
-- This optimizes queries that filter by recent datetime
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_status_recent 
ON live_status_minute_table(datetime DESC) 
WHERE datetime >= NOW() - INTERVAL '7 DAYS';

-- Composite index for live status filtering
-- This supports queries that filter by both live_status and datetime
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_status_status_datetime 
ON live_status_minute_table(live_status, datetime DESC)
WHERE datetime >= NOW() - INTERVAL '7 DAYS';

-- Index for room_id and live_status combination
-- This supports queries that need to filter by room and status
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_status_room_status 
ON live_status_minute_table(room_id, live_status, datetime DESC);
