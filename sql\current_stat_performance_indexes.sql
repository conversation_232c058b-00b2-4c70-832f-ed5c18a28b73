-- Performance optimization indexes for current_stat_table queries
-- These indexes will significantly improve query performance for the read_basic_all_stat endpoint

-- Primary index for current_stat_table: uid and datetime (most critical)
-- This supports the main query filtering by uid and datetime range
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_current_stat_uid_datetime 
ON current_stat_table(uid, datetime DESC);

-- Index for uid only (for queries without datetime filter)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_current_stat_uid 
ON current_stat_table(uid);

-- Partial index for recent data (last 30 days)
-- This optimizes queries that filter by recent datetime
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_current_stat_recent 
ON current_stat_table(uid, datetime DESC) 
WHERE datetime >= NOW() - INTERVAL '30 DAYS';

-- Partial index for very recent data (last 7 days)
-- This optimizes queries for very recent data which are most common
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_current_stat_very_recent 
ON current_stat_table(uid, datetime DESC) 
WHERE datetime >= NOW() - INTERVAL '7 DAYS';

-- Index on datetime only for global queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_current_stat_datetime 
ON current_stat_table(datetime DESC);

-- Composite index for covering queries (includes commonly selected columns)
-- This can serve as a covering index for the main query
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_current_stat_covering 
ON current_stat_table(uid, datetime DESC) 
INCLUDE (video_total_num, article_total_num, likes_total_num, elec_num, follower_num, dahanghai_num);
