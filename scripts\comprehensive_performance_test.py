#!/usr/bin/env python3
"""
Comprehensive performance test to validate all optimizations
"""

import asyncio
import time
import statistics
from backend.query.query_board_data import collect_all_live_status_stuff
from backend.cache import cache_manager, is_redis_available
from logger import logger

class PerformanceTestSuite:
    """Comprehensive performance test suite"""
    
    def __init__(self):
        self.results = {}
        self.test_iterations = 10
    
    async def test_database_optimization(self):
        """Test database query optimization"""
        logger.info("Testing database optimization...")
        
        # Clear cache to ensure database hits
        cache_manager.clear_pattern("live_status*")
        
        times = []
        for i in range(3):  # Fewer iterations for database tests
            start_time = time.time()
            result = await collect_all_live_status_stuff()
            query_time = time.time() - start_time
            times.append(query_time)
            
            logger.info(f"Database query {i+1}: {query_time:.3f}s, {len(result) if result else 0} records")
            
            # Clear cache between tests
            cache_manager.clear_pattern("live_status*")
            await asyncio.sleep(0.5)
        
        avg_time = statistics.mean(times)
        self.results['database_avg_time'] = avg_time
        self.results['database_record_count'] = len(result) if result else 0
        
        logger.info(f"Database optimization - Average time: {avg_time:.3f}s")
        return avg_time
    
    async def test_caching_performance(self):
        """Test caching performance"""
        logger.info("Testing caching performance...")
        
        # Clear cache first
        cache_manager.clear_pattern("live_status*")
        
        # First call (database)
        start_time = time.time()
        result1 = await collect_all_live_status_stuff()
        first_call_time = time.time() - start_time
        
        # Subsequent calls (cache)
        cache_times = []
        for i in range(5):
            start_time = time.time()
            result2 = await collect_all_live_status_stuff()
            cache_time = time.time() - start_time
            cache_times.append(cache_time)
        
        avg_cache_time = statistics.mean(cache_times)
        cache_speedup = first_call_time / avg_cache_time if avg_cache_time > 0 else 0
        
        self.results['first_call_time'] = first_call_time
        self.results['avg_cache_time'] = avg_cache_time
        self.results['cache_speedup'] = cache_speedup
        
        logger.info(f"Caching performance - First: {first_call_time:.3f}s, Cached: {avg_cache_time:.3f}s")
        logger.info(f"Cache speedup: {cache_speedup:.1f}x faster")
        
        return first_call_time, avg_cache_time
    
    async def test_concurrent_performance(self):
        """Test concurrent request performance"""
        logger.info("Testing concurrent performance...")
        
        # Clear cache first
        cache_manager.clear_pattern("live_status*")
        
        async def single_request(request_id):
            start_time = time.time()
            result = await collect_all_live_status_stuff()
            request_time = time.time() - start_time
            return request_time, len(result) if result else 0
        
        concurrent_results = {}
        
        for concurrency in [1, 5, 10, 20]:
            logger.info(f"Testing {concurrency} concurrent requests...")
            
            start_time = time.time()
            tasks = [single_request(i+1) for i in range(concurrency)]
            results = await asyncio.gather(*tasks)
            total_time = time.time() - start_time
            
            successful_results = [r for r in results if r[0] is not None]
            
            if successful_results:
                avg_request_time = statistics.mean([r[0] for r in successful_results])
                concurrent_results[concurrency] = {
                    'total_time': total_time,
                    'avg_request_time': avg_request_time,
                    'success_rate': len(successful_results) / concurrency * 100
                }
                
                logger.info(f"Concurrency {concurrency}: {total_time:.3f}s total, "
                           f"{avg_request_time:.3f}s avg, "
                           f"{len(successful_results)}/{concurrency} successful")
            
            await asyncio.sleep(1)
        
        self.results['concurrent_performance'] = concurrent_results
        return concurrent_results
    
    async def test_memory_usage(self):
        """Test memory usage patterns"""
        logger.info("Testing memory usage...")
        
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        # Baseline memory
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Clear cache
        cache_manager.clear_pattern("live_status*")
        
        # Load data multiple times
        for i in range(10):
            await collect_all_live_status_stuff()
        
        # Check memory after operations
        after_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = after_memory - baseline_memory
        
        self.results['baseline_memory_mb'] = baseline_memory
        self.results['after_memory_mb'] = after_memory
        self.results['memory_increase_mb'] = memory_increase
        
        logger.info(f"Memory usage - Baseline: {baseline_memory:.1f}MB, "
                   f"After: {after_memory:.1f}MB, "
                   f"Increase: {memory_increase:.1f}MB")
        
        return baseline_memory, after_memory
    
    async def test_error_handling(self):
        """Test error handling and recovery"""
        logger.info("Testing error handling...")
        
        # Test with invalid cache operations
        try:
            cache_manager.clear_pattern("invalid*pattern")
            logger.info("✅ Cache error handling working")
        except Exception as e:
            logger.warning(f"Cache error handling issue: {e}")
        
        # Test function resilience
        try:
            result = await collect_all_live_status_stuff()
            if result:
                logger.info("✅ Function error handling working")
            else:
                logger.warning("Function returned empty result")
        except Exception as e:
            logger.error(f"Function error handling failed: {e}")
    
    def generate_performance_report(self):
        """Generate comprehensive performance report"""
        
        report = []
        report.append("=" * 80)
        report.append("COMPREHENSIVE PERFORMANCE TEST REPORT")
        report.append("=" * 80)
        
        # System Information
        report.append(f"\nSYSTEM INFORMATION:")
        report.append(f"Redis Available: {is_redis_available()}")
        report.append(f"Cache Backend: {'Redis' if is_redis_available() else 'Memory'}")
        
        # Database Performance
        if 'database_avg_time' in self.results:
            report.append(f"\nDATABASE OPTIMIZATION:")
            report.append(f"Average Query Time: {self.results['database_avg_time']:.3f}s")
            report.append(f"Records Retrieved: {self.results['database_record_count']}")
            
            # Performance rating
            db_time = self.results['database_avg_time']
            if db_time < 0.1:
                rating = "EXCELLENT"
            elif db_time < 0.5:
                rating = "GOOD"
            elif db_time < 1.0:
                rating = "ACCEPTABLE"
            else:
                rating = "NEEDS IMPROVEMENT"
            report.append(f"Database Performance: {rating}")
        
        # Caching Performance
        if 'cache_speedup' in self.results:
            report.append(f"\nCACHING PERFORMANCE:")
            report.append(f"First Call (Database): {self.results['first_call_time']:.3f}s")
            report.append(f"Cached Calls: {self.results['avg_cache_time']:.3f}s")
            report.append(f"Cache Speedup: {self.results['cache_speedup']:.1f}x faster")
            
            improvement = ((self.results['first_call_time'] - self.results['avg_cache_time']) / 
                          self.results['first_call_time'] * 100)
            report.append(f"Performance Improvement: {improvement:.1f}%")
        
        # Concurrent Performance
        if 'concurrent_performance' in self.results:
            report.append(f"\nCONCURRENT PERFORMANCE:")
            for concurrency, data in self.results['concurrent_performance'].items():
                report.append(f"  {concurrency} concurrent: {data['avg_request_time']:.3f}s avg, "
                             f"{data['success_rate']:.0f}% success")
        
        # Memory Usage
        if 'memory_increase_mb' in self.results:
            report.append(f"\nMEMORY USAGE:")
            report.append(f"Baseline: {self.results['baseline_memory_mb']:.1f}MB")
            report.append(f"After Tests: {self.results['after_memory_mb']:.1f}MB")
            report.append(f"Increase: {self.results['memory_increase_mb']:.1f}MB")
        
        # Overall Assessment
        report.append(f"\nOVERALL ASSESSMENT:")
        
        # Calculate overall score
        score = 0
        max_score = 0
        
        # Database score (0-25 points)
        if 'database_avg_time' in self.results:
            db_time = self.results['database_avg_time']
            if db_time < 0.1:
                score += 25
            elif db_time < 0.5:
                score += 20
            elif db_time < 1.0:
                score += 15
            else:
                score += 10
            max_score += 25
        
        # Caching score (0-35 points)
        if 'cache_speedup' in self.results:
            speedup = self.results['cache_speedup']
            if speedup > 100:
                score += 35
            elif speedup > 50:
                score += 30
            elif speedup > 10:
                score += 25
            else:
                score += 15
            max_score += 35
        
        # Concurrency score (0-25 points)
        if 'concurrent_performance' in self.results:
            concurrent_data = self.results['concurrent_performance']
            if 20 in concurrent_data and concurrent_data[20]['success_rate'] == 100:
                score += 25
            elif 10 in concurrent_data and concurrent_data[10]['success_rate'] == 100:
                score += 20
            elif 5 in concurrent_data and concurrent_data[5]['success_rate'] == 100:
                score += 15
            else:
                score += 10
            max_score += 25
        
        # Memory score (0-15 points)
        if 'memory_increase_mb' in self.results:
            memory_increase = self.results['memory_increase_mb']
            if memory_increase < 10:
                score += 15
            elif memory_increase < 50:
                score += 10
            else:
                score += 5
            max_score += 15
        
        if max_score > 0:
            overall_score = (score / max_score) * 100
            report.append(f"Performance Score: {score}/{max_score} ({overall_score:.1f}%)")
            
            if overall_score >= 90:
                grade = "A+ (EXCELLENT)"
            elif overall_score >= 80:
                grade = "A (VERY GOOD)"
            elif overall_score >= 70:
                grade = "B (GOOD)"
            elif overall_score >= 60:
                grade = "C (ACCEPTABLE)"
            else:
                grade = "D (NEEDS IMPROVEMENT)"
            
            report.append(f"Overall Grade: {grade}")
        
        report.append("=" * 80)
        
        return "\n".join(report)
    
    async def run_all_tests(self):
        """Run all performance tests"""
        
        logger.info("Starting comprehensive performance test suite...")
        
        try:
            # Run all tests
            await self.test_database_optimization()
            await self.test_caching_performance()
            await self.test_concurrent_performance()
            await self.test_memory_usage()
            await self.test_error_handling()
            
            # Generate and display report
            report = self.generate_performance_report()
            logger.info("\n" + report)
            
            return self.results
            
        except Exception as e:
            logger.error(f"Performance test suite failed: {e}")
            return None

async def main():
    """Main test execution"""
    test_suite = PerformanceTestSuite()
    results = await test_suite.run_all_tests()
    
    if results:
        logger.info("Performance test suite completed successfully!")
    else:
        logger.error("Performance test suite failed!")

if __name__ == "__main__":
    asyncio.run(main())
