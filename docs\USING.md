# VupBI API 接口文档

本文档详细描述了 VupBI 系统中所有可用的 API 接口，面向运维人员提供完整的接口使用指南。

## 基础信息

- **服务地址**: http://localhost:9022
- **数据格式**: JSON
- **字符编码**: UTF-8
- **时区**: 东八区 (UTC+8)

## 标准响应格式

所有接口都遵循统一的响应格式：

```json
{
    "code": 200,
    "message": "Success",
    "data": {...}
}
```

## 接口分类

### 1. 用户认证管理

| HTTP方法 | 接口路径 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|
| POST | `/web/users` | 创建新用户 | `email`(必需), `username`(必需), `password`(必需), `phone`(可选) | 用户信息和ID |
| POST | `/web/auth/sessions` | 用户登录认证 | `email`(必需), `password`(必需) | 访问令牌和用户信息 |
| PATCH | `/web/users/password` | 修改用户密码 | `email`(必需), `new_password`(必需) | 操作结果 |
| DELETE | `/web/users` | 删除用户 | `email`(必需) | 无内容(204) |

### 2. 基础信息查询

| HTTP方法 | 接口路径 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|
| GET | `/` | 系统根路径 | 无 | 系统欢迎信息 |
| GET | `/basic/vups` | 获取所有VUP列表 | 无 | VUP配置列表 |
| GET | `/basic/mid` | 获取VUP的UID | `vtuber_name`(可选,默认"星瞳") | VUP的UID信息 |
| GET | `/basic/info` | 获取VUP基础信息 | `vtuber`(可选,默认"星瞳") | VUP详细资料 |

### 3. 粉丝数据管理

| HTTP方法 | 接口路径 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|
| GET | `/basic/follower/current` | 获取当前粉丝数 | `vtuber_name`(可选,默认"星瞳") | 当前粉丝数量 |
| GET | `/basic/follower/all` | 获取粉丝历史数据 | `vtuber_name`(可选), `recent`(可选,-1为全部), `limit`(可选,1000), `offset`(可选,0) | 粉丝数历史记录 |
| GET | `/basic/follower/rate` | 获取粉丝增长率 | `vtuber`(可选,默认"星瞳"), `recent`(可选,90天) | 粉丝增长率百分比 |
| GET | `/basic/follower/rise` | 获取粉丝变化数量 | `vtuber`(可选,默认"星瞳"), `recent`(可选,1天) | 粉丝变化数值 |
| GET | `/follower/new_list` | 获取新增粉丝列表 | `recent`(可选,7天), `vtuber`(可选,默认"星瞳") | 新粉丝详细信息 |
| GET | `/follower/review_list` | 获取粉丝观看状态 | `recent`(可选,7天), `vtuber`(可选,默认"星瞳") | 粉丝观看记录 |
| GET | `/follower/review_rate` | 获取粘性粉丝比率 | `recent`(可选,7天), `vtuber`(可选,默认"星瞳") | 粉丝粘性百分比 |

### 4. 大航海数据管理

| HTTP方法 | 接口路径 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|
| GET | `/basic/dahanghai/current` | 获取当前大航海数 | `vtuber_name`(可选,默认"星瞳") | 当前大航海数量 |
| GET | `/basic/dahanghai/all` | 获取大航海历史数据 | `vtuber_name`(可选), `recent`(可选,-1为全部), `limit`(可选,1000), `offset`(可选,0) | 大航海历史记录 |
| GET | `/basic/dahanghai/rate` | 获取大航海增长率 | `vtuber`(可选,默认"星瞳"), `recent`(可选,90天) | 大航海增长率 |
| GET | `/basic/dahanghai/rise` | 获取大航海变化数量 | `vtuber`(可选,默认"星瞳"), `recent`(可选,1天) | 大航海变化数值 |

### 5. 统计数据查询

| HTTP方法 | 接口路径 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|
| GET | `/basic/stat/current` | 获取当前统计数据 | `vtuber`(可选,默认"星瞳") | 当前各项统计指标 |
| GET | `/basic/stat/all` | 获取历史统计数据 | `vtuber`(可选,默认"星瞳"), `recent`(可选,-1为全部) | 历史统计记录 |
| GET | `/basic/stats/period` | 获取时间段统计 | `vtuber_name`(必需), `start_date`(必需,YYYY-MM-DD), `end_date`(必需,YYYY-MM-DD) | 指定时间段统计 |

### 6. 粉丝牌排行

| HTTP方法 | 接口路径 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|
| GET | `/basic/medal_rank` | 获取最新粉丝牌排行 | `vtuber`(可选,默认"星瞳") | 粉丝牌排行榜(前10) |
| GET | `/basic/medal_rank/target` | 获取指定日期排行 | `target_datetime`(必需,YYYY-MM-DD), `vtuber`(可选,默认"星瞳") | 指定日期排行榜 |

### 7. 直播信息管理

| HTTP方法 | 接口路径 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|
| GET | `/live/info` | 获取当前直播信息 | `vtuber`(可选,默认"星瞳") | 直播状态和详情 |
| GET | `/live/sessions` | 获取所有直播场次 | `vtuber`(可选,默认"星瞳") | 直播场次列表 |
| GET | `/live/one_session` | 获取单个直播详情 | `liveId`(必需) | 直播场次详细信息 |
| GET | `/live/one_session/minute` | 获取直播分钟级数据 | `liveId`(必需), `interval`(可选,10分钟) | 直播分钟级统计 |

### 8. 动态内容管理

| HTTP方法 | 接口路径 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|
| GET | `/dynamics/current` | 获取最新动态 | `vtuber`(可选,默认"星瞳") | 最新动态内容 |
| GET | `/dynamics/list` | 获取动态列表 | `vtuber_name`(可选), `start_date`(可选), `end_date`(可选), `limit`(可选,10), `sort`(可选,"popularity"), `offset`(可选,0) | 动态列表 |

### 9. 视频内容管理

| HTTP方法 | 接口路径 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|
| GET | `/video/current` | 获取最新视频 | `vtuber`(可选,默认"星瞳") | 最新视频信息 |
| GET | `/video/list` | 获取视频列表 | `vtuber`(可选), `start_date`(可选), `end_date`(可选), `limit`(可选,10), `sort`(可选,"popularity"), `offset`(可选,0) | 视频列表 |
| GET | `/video/conclusion` | 获取视频AI总结 | `bvid`(必需) | 视频AI分析结果 |
| GET | `/video/views/current` | 获取当前播放量 | `bvid`(必需) | 当前播放数据 |
| GET | `/video/views/recent` | 获取近期播放数据 | `bvid`(必需), `recent`(必需,天数) | 近期播放趋势 |
| GET | `/video/views/target` | 获取指定日期播放量 | `bvid`(必需), `time`(必需,YYYY-MM-DD) | 指定日期播放数据 |
| GET | `/video/views/top_n/day` | 获取单日热门视频 | `time`(必需,YYYY-MM-DD), `n`(必需), `vtuber`(可选,默认"星瞳") | 单日热门视频排行 |
| GET | `/video/views/top_n/period` | 获取时间段热门视频 | `s`(必需,开始日期), `e`(必需,结束日期), `n`(必需), `vtuber`(可选,默认"星瞳") | 时间段热门视频 |

### 10. 评论数据分析

| HTTP方法 | 接口路径 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|
| GET | `/comment/video` | 获取视频评论 | `vtuber`(可选), `start_date`(可选), `end_date`(可选), `limit`(可选,10), `sort`(可选,"popularity"), `offset`(可选,0) | 视频评论列表 |
| GET | `/comment/dynamic` | 获取动态评论 | `vtuber`(可选), `start_date`(可选), `end_date`(可选), `limit`(可选,10), `sort`(可选,"popularity"), `offset`(可选,0) | 动态评论列表 |
| GET | `/comment/top_n` | 获取热门评论 | `s`(必需,开始日期), `e`(必需,结束日期), `n`(必需), `source`(可选,"video"), `vtuber`(可选,默认"星瞳") | 热门评论排行 |
| GET | `/comment/top_n_user` | 获取活跃评论用户 | `s`(必需,开始日期), `e`(必需,结束日期), `n`(必需), `source`(可选,"all"), `vtuber`(可选,默认"星瞳") | 活跃用户排行 |
| GET | `/comment/wordcloud` | 生成评论词云 | `s`(必需,开始日期), `e`(必需,结束日期), `vtuber`(可选,默认"星瞳") | 词云图片路径 |

### 11. 贴吧数据分析

| HTTP方法 | 接口路径 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|
| GET | `/tieba/whole` | 获取贴吧完整数据 | `s`(必需,开始日期), `e`(必需,结束日期), `vtuber`(可选,默认"星瞳") | 贴吧帖子和评论 |
| GET | `/tieba/threads` | 获取贴吧主题帖 | `s`(必需,开始日期), `e`(必需,结束日期), `vtuber`(可选,默认"星瞳") | 贴吧主题帖列表 |

### 12. AI分析功能

| HTTP方法 | 接口路径 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|
| GET | `/llm/relations` | 获取联动关系分析 | `recent`(可选,30天), `vtuber`(可选,默认"星瞳") | 联动关系信息 |
| GET | `/llm/sensiment` | 获取情感分析(全平台) | `recent`(可选,30天), `vtuber`(可选,默认"星瞳") | 情感分析结果 |
| GET | `/llm/sensiment/bili` | 获取B站情感分析 | `recent`(可选,30天), `vtuber`(可选,默认"星瞳") | B站情感分析 |
| GET | `/llm/sensiment/tieba` | 获取贴吧情感分析 | `recent`(可选,30天), `vtuber`(可选,默认"星瞳") | 贴吧情感分析 |
| GET | `/llm/topics` | 获取话题分析 | `recent`(可选,30天), `vtuber`(可选,默认"星瞳") | 热门话题分析 |

### 13. 数据流向分析

| HTTP方法 | 接口路径 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|
| GET | `/board/info` | 获取看板信息 | 无 | 所有VUP基础信息 |
| GET | `/board/dahanghai/whole` | 获取整体大航海流向 | `c`(必需,当前日期), `p`(必需,之前日期) | 大航海流向分析 |
| GET | `/board/dahanghai/target` | 获取目标VUP流向 | `target`(必需,VUP名称), `c`(必需,当前日期), `p`(必需,之前日期), `n`(可选,5) | 目标VUP流向 |
| GET | `/board/dahanghai/target_period` | 获取时间段流向分析 | `target`(必需), `start_time_str1`(必需), `end_time_str1`(必需), `start_time_str2`(必需), `end_time_str2`(必需), `n`(可选,5) | 时间段流向对比 |

### 14. 创作者数据分析

| HTTP方法 | 接口路径 | 功能描述 | 请求参数 | 响应格式 |
|---------|---------|---------|---------|---------|
| GET | `/creator/overview/stat/by-date-range` | 获取创作者概览统计(时间段) | `uid`(必需), `start_date`(必需), `end_date`(必需), `limit`(可选,1000), `offset`(可选,0) | 创作者统计数据 |
| GET | `/creator/overview/stat/by-date` | 获取创作者概览统计(单日) | `uid`(必需), `date`(必需), `limit`(可选,1000), `offset`(可选,0) | 单日统计数据 |
| GET | `/creator/attention/analyze/by-date-range` | 获取关注度分析(时间段) | `uid`(必需), `start_date`(必需), `end_date`(必需), `limit`(可选,1000), `offset`(可选,0) | 关注度分析数据 |
| GET | `/creator/attention/analyze/by-date` | 获取关注度分析(单日) | `uid`(必需), `date`(必需), `limit`(可选,1000), `offset`(可选,0) | 单日关注度数据 |
| GET | `/creator/archive/analyze/by-date-range` | 获取视频分析(时间段) | `uid`(必需), `start_date`(必需), `end_date`(必需), `period`(可选), `limit`(可选,1000), `offset`(可选,0) | 视频分析数据 |
| GET | `/creator/archive/analyze/by-date` | 获取视频分析(单日) | `uid`(必需), `date`(必需), `period`(可选), `limit`(可选,1000), `offset`(可选,0) | 单日视频数据 |
| GET | `/creator/fan/graph/by-date-range` | 获取粉丝图表(时间段) | `uid`(必需), `start_date`(必需), `end_date`(必需), `limit`(可选,1000), `offset`(可选,0) | 粉丝图表数据 |
| GET | `/creator/fan/graph/by-date` | 获取粉丝图表(单日) | `uid`(必需), `date`(必需), `limit`(可选,1000), `offset`(可选,0) | 单日粉丝图表 |
| GET | `/creator/fan/overview/by-date-range` | 获取粉丝概览(时间段) | `uid`(必需), `start_date`(必需), `end_date`(必需), `limit`(可选,1000), `offset`(可选,0) | 粉丝概览数据 |
| GET | `/creator/fan/overview/by-date` | 获取粉丝概览(单日) | `uid`(必需), `date`(必需), `limit`(可选,1000), `offset`(可选,0) | 单日粉丝概览 |
| GET | `/creator/video/compare/by-date-range` | 获取视频对比(时间段) | `uid`(必需), `start_date`(必需), `end_date`(必需), `limit`(可选,1000), `offset`(可选,0) | 视频对比数据 |
| GET | `/creator/video/compare/by-date` | 获取视频对比(单日) | `uid`(必需), `date`(必需), `limit`(可选,1000), `offset`(可选,0) | 单日视频对比 |
| GET | `/creator/video/pandect/by-date-range` | 获取视频全景(时间段) | `uid`(必需), `start_date`(必需), `end_date`(必需), `limit`(可选,1000), `offset`(可选,0) | 视频全景数据 |
| GET | `/creator/video/pandect/by-date` | 获取视频全景(单日) | `uid`(必需), `date`(必需), `limit`(可选,1000), `offset`(可选,0) | 单日视频全景 |
| GET | `/creator/video/survey/by-date-range` | 获取视频调研(时间段) | `uid`(必需), `start_date`(必需), `end_date`(必需), `limit`(可选,1000), `offset`(可选,0) | 视频调研数据 |
| GET | `/creator/video/survey/by-date` | 获取视频调研(单日) | `uid`(必需), `date`(必需), `limit`(可选,1000), `offset`(可选,0) | 单日视频调研 |
| GET | `/creator/video/source/by-date-range` | 获取视频来源(时间段) | `uid`(必需), `start_date`(必需), `end_date`(必需), `limit`(可选,1000), `offset`(可选,0) | 视频来源数据 |
| GET | `/creator/video/source/by-date` | 获取视频来源(单日) | `uid`(必需), `date`(必需), `limit`(可选,1000), `offset`(可选,0) | 单日视频来源 |
| GET | `/creator/video/view-data/by-date-range` | 获取视频观看数据(时间段) | `uid`(必需), `start_date`(必需), `end_date`(必需), `limit`(可选,1000), `offset`(可选,0) | 视频观看数据 |
| GET | `/creator/video/view-data/by-date` | 获取视频观看数据(单日) | `uid`(必需), `date`(必需), `limit`(可选,1000), `offset`(可选,0) | 单日观看数据 |

## 使用示例

### 1. 获取VUP当前粉丝数

```bash
curl -X GET "http://localhost:9022/basic/follower/current?vtuber_name=星瞳"
```

**响应示例:**
```json
{
    "code": 200,
    "message": "Current follower count retrieved successfully",
    "data": {
        "vtuber_name": "星瞳",
        "mid": "401315430",
        "current_followers": 1024459,
        "timestamp": "2025-08-11T15:09:01.648869"
    }
}
```

### 2. 获取热门评论

```bash
curl -X GET "http://localhost:9022/comment/top_n?vtuber=星瞳&s=2023-01-01&e=2024-12-31&n=10&source=video"
```

**响应示例:**
```json
{
    "code": 200,
    "message": "Top N comments by heat retrieved successfully",
    "data": {
        "vtuber_name": "星瞳",
        "mid": "401315430",
        "query_params": {
            "start_date": "2023-01-01",
            "end_date": "2024-12-31",
            "limit": 10,
            "source": "video",
            "period_days": 365
        },
        "top_comments": [
            {
                "name": "洛恩佐Lorenzo",
                "uid": 1097927383,
                "face": "https://i0.hdslb.com/bfs/face/example.jpg",
                "time": "2024-08-31 12:09:03",
                "comment": "太专业啦，希望以后还能在别的国产游戏里听到星瞳的配音[猴哥]",
                "heat": 3449,
                "from_id": "BV1JS421Q7rX",
                "from_name": "【黑神话:悟空】虚拟偶像给国产3A配音？超长角色配音纪实！",
                "source": "video"
            }
        ],
        "total_count": 1,
        "retrieved_at": "2025-08-04T12:00:00"
    }
}
```

### 3. 用户登录

```bash
curl -X POST "http://localhost:9022/web/auth/sessions" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

**响应示例:**
```json
{
    "code": 200,
    "message": "Authentication successful",
    "data": {
        "access_token": "fake-jwt-token-for-username-123",
        "token_type": "bearer",
        "user": {
            "id": 123,
            "username": "username",
            "email": "<EMAIL>"
        }
    }
}
```

## 错误处理

### 常见错误码

| 错误码 | 说明 | 处理建议 |
|-------|------|---------|
| 400 | 请求参数错误 | 检查参数格式和必需参数 |
| 401 | 认证失败 | 检查用户凭据或重新登录 |
| 404 | 资源不存在 | 检查VUP名称或数据是否存在 |
| 500 | 服务器内部错误 | 联系系统管理员 |

### 错误响应格式

```json
{
    "code": 400,
    "message": "Parameter 'vtuber' cannot be empty",
    "details": [
        {
            "field": "vtuber",
            "message": "Parameter cannot be empty",
            "code": "REQUIRED_FIELD"
        }
    ]
}
```

## 注意事项

1. **时间格式**: 所有日期参数使用 `YYYY-MM-DD` 格式，时间戳使用 `YYYY-MM-DD HH:MM:SS` 格式
2. **分页参数**: `limit` 最大值通常为1000，`offset` 从0开始
3. **VUP名称**: 支持中文名称、英文名称、UID等多种格式
4. **数据时效性**: 部分数据可能存在延迟，建议查看 `retrieved_at` 字段确认数据时间
5. **请求频率**: 建议控制请求频率，避免对系统造成过大压力

## 联系方式

如有问题或需要技术支持，请联系系统管理员。

