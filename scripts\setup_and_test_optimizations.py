#!/usr/bin/env python3
"""
Comprehensive setup and testing script for API endpoint optimizations
This script:
1. Applies database performance indexes
2. Tests the optimized endpoints
3. Provides performance comparison and recommendations
"""

import asyncio
import subprocess
import sys
import os
import json
from pathlib import Path
from datetime import datetime

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from logger import logger


class OptimizationSetup:
    def __init__(self):
        self.project_root = project_root
        self.results = {}
    
    async def check_server_status(self):
        """Check if the backend server is running"""
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get("http://localhost:9022/", timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        logger.info("✓ Backend server is running on http://localhost:9022")
                        return True
        except Exception as e:
            logger.error(f"✗ Backend server is not accessible: {e}")
            logger.error("Please start the backend server first:")
            logger.error("  conda activate clct")
            logger.error("  cd backend && python -m uvicorn app:app --host 0.0.0.0 --port 9022")
            return False
    
    async def apply_database_indexes(self):
        """Apply performance indexes to the database"""
        logger.info("Applying database performance indexes...")
        
        try:
            # Run the index application script
            script_path = self.project_root / "scripts" / "apply_endpoint_indexes.py"
            
            if not script_path.exists():
                logger.error(f"Index script not found: {script_path}")
                return False
            
            # Execute the script
            process = await asyncio.create_subprocess_exec(
                sys.executable, str(script_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=str(self.project_root)
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.info("✓ Database indexes applied successfully")
                logger.info(stdout.decode())
                return True
            else:
                logger.error("✗ Failed to apply database indexes")
                logger.error(stderr.decode())
                return False
                
        except Exception as e:
            logger.error(f"Error applying database indexes: {e}")
            return False
    
    async def run_performance_tests(self):
        """Run performance tests on the optimized endpoints"""
        logger.info("Running performance tests...")
        
        try:
            # Run the performance test script
            script_path = self.project_root / "scripts" / "test_endpoint_performance.py"
            
            if not script_path.exists():
                logger.error(f"Performance test script not found: {script_path}")
                return False
            
            # Execute the script
            process = await asyncio.create_subprocess_exec(
                sys.executable, str(script_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=str(self.project_root)
            )
            
            stdout, stderr = await process.communicate()
            
            logger.info("Performance test output:")
            logger.info(stdout.decode())
            
            if stderr:
                logger.warning("Performance test warnings/errors:")
                logger.warning(stderr.decode())
            
            # Try to find and load the results file
            results_files = list(self.project_root.glob("performance_test_results_*.json"))
            if results_files:
                latest_results = max(results_files, key=lambda f: f.stat().st_mtime)
                with open(latest_results, 'r', encoding='utf-8') as f:
                    self.results['performance_tests'] = json.load(f)
                logger.info(f"Performance test results loaded from: {latest_results}")
            
            return process.returncode == 0
            
        except Exception as e:
            logger.error(f"Error running performance tests: {e}")
            return False
    
    async def check_cache_health(self):
        """Check cache health and statistics"""
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get("http://localhost:9022/admin/cache/stats") as response:
                    if response.status == 200:
                        cache_stats = await response.json()
                        self.results['cache_stats'] = cache_stats
                        logger.info("✓ Cache statistics retrieved")
                        
                        # Print cache health summary
                        data = cache_stats.get('data', {})
                        overall = data.get('overall', {})
                        
                        logger.info(f"Cache Health: {overall.get('health_status', 'unknown')}")
                        logger.info(f"Average Hit Ratio: {overall.get('average_hit_ratio', 0):.2%}")
                        logger.info(f"Capacity Utilization: {overall.get('capacity_utilization', 0):.2%}")
                        
                        return True
                    else:
                        logger.warning("Could not retrieve cache statistics")
                        return False
        except Exception as e:
            logger.warning(f"Could not check cache health: {e}")
            return False
    
    def generate_optimization_report(self):
        """Generate a comprehensive optimization report"""
        logger.info("Generating optimization report...")
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "optimization_summary": {
                "database_indexes": "Applied performance indexes for 5 target endpoints",
                "query_optimization": "Implemented optimized query functions with reduced database round trips",
                "caching_layer": "Added LRU caching with TTL for frequently accessed data",
                "endpoint_modifications": "Updated 5 endpoints to use optimized functions and caching"
            },
            "optimized_endpoints": [
                "/live/sessions - Live streaming sessions for a VTuber",
                "/live/one_session - Complete information for a specific live session",
                "/live/one_session/minute - Live session with minute-by-minute data",
                "/board/dahanghai/target - Dahanghai flow analysis for a specific VTuber",
                "/board/dahanghai/target_period - Dahanghai flow analysis between periods"
            ],
            "performance_improvements": {
                "database_queries": "Reduced from N+1 queries to single optimized queries",
                "memory_usage": "Database-level set operations instead of Python set operations",
                "response_caching": "LRU cache with TTL for frequently accessed data",
                "index_optimization": "Specific indexes for query patterns used by endpoints"
            }
        }
        
        # Add test results if available
        if 'performance_tests' in self.results:
            report['test_results'] = self.results['performance_tests']
        
        if 'cache_stats' in self.results:
            report['cache_statistics'] = self.results['cache_stats']
        
        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = self.project_root / f"optimization_report_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Optimization report saved to: {report_file}")
        
        # Print summary
        self.print_optimization_summary(report)
        
        return report_file
    
    def print_optimization_summary(self, report):
        """Print a summary of optimizations"""
        print("\n" + "="*80)
        print("API ENDPOINT OPTIMIZATION SUMMARY")
        print("="*80)
        
        print("\n🚀 OPTIMIZATIONS APPLIED:")
        for key, value in report['optimization_summary'].items():
            print(f"  ✓ {key.replace('_', ' ').title()}: {value}")
        
        print("\n📊 OPTIMIZED ENDPOINTS:")
        for endpoint in report['optimized_endpoints']:
            print(f"  • {endpoint}")
        
        print("\n⚡ PERFORMANCE IMPROVEMENTS:")
        for key, value in report['performance_improvements'].items():
            print(f"  • {key.replace('_', ' ').title()}: {value}")
        
        # Performance test summary
        if 'test_results' in report:
            test_results = report['test_results']
            print("\n📈 PERFORMANCE TEST RESULTS:")
            
            successful_endpoints = 0
            total_endpoints = 0
            total_avg_time = 0
            
            for endpoint_name, result in test_results.items():
                if endpoint_name in ['cache_performance', 'cache_stats']:
                    continue
                
                total_endpoints += 1
                success_rate = result.get('success_rate', 0)
                avg_time = result.get('avg_response_time_ms', 0)
                
                status = "✓" if success_rate == 100 else "✗"
                print(f"  {status} {endpoint_name}: {avg_time:.2f}ms ({success_rate:.1f}% success)")
                
                if success_rate == 100:
                    successful_endpoints += 1
                    total_avg_time += avg_time
            
            print(f"\n  Summary: {successful_endpoints}/{total_endpoints} endpoints passed")
            if successful_endpoints > 0:
                avg_response_time = total_avg_time / successful_endpoints
                print(f"  Average response time: {avg_response_time:.2f}ms")
            
            # Cache performance
            if 'cache_performance' in test_results:
                cache_perf = test_results['cache_performance']
                improvement = cache_perf.get('improvement_percentage', 0)
                print(f"  Cache performance improvement: {improvement:.1f}%")
        
        print("\n" + "="*80)
        print("NEXT STEPS:")
        print("1. Monitor endpoint performance in production")
        print("2. Check cache hit ratios: GET /admin/cache/stats")
        print("3. Clear caches if needed: POST /admin/cache/clear")
        print("4. Consider additional optimizations based on usage patterns")
        print("="*80)


async def main():
    """Main function to run the complete optimization setup and testing"""
    setup = OptimizationSetup()
    
    logger.info("Starting API endpoint optimization setup and testing...")
    
    # Step 1: Check server status
    if not await setup.check_server_status():
        return 1
    
    # Step 2: Apply database indexes
    if not await setup.apply_database_indexes():
        logger.error("Failed to apply database indexes. Continuing with tests...")
    
    # Step 3: Run performance tests
    if not await setup.run_performance_tests():
        logger.error("Performance tests failed or had issues")
    
    # Step 4: Check cache health
    await setup.check_cache_health()
    
    # Step 5: Generate report
    report_file = setup.generate_optimization_report()
    
    logger.info("Optimization setup and testing completed!")
    logger.info(f"Full report available at: {report_file}")
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
