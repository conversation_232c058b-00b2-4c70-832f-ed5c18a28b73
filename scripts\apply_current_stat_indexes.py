#!/usr/bin/env python3
"""
Apply performance indexes for current_stat_table to improve query performance.
This script creates indexes that will significantly improve the performance of 
the /basic/stat/all endpoint.
"""

import asyncio
import sys
import os

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sql.db_pool import get_connection, initialize_pool
from server.base.config import get_logger

logger = get_logger()

async def apply_current_stat_indexes():
    """Apply performance indexes for current_stat_table"""
    
    indexes = [
        {
            "name": "idx_current_stat_uid_datetime",
            "sql": """
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_current_stat_uid_datetime 
                ON current_stat_table(uid, datetime DESC);
            """
        },
        {
            "name": "idx_current_stat_uid", 
            "sql": """
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_current_stat_uid 
                ON current_stat_table(uid);
            """
        },
        {
            "name": "idx_current_stat_recent",
            "sql": """
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_current_stat_recent
                ON current_stat_table(uid, datetime DESC)
                WHERE datetime >= CURRENT_DATE - INTERVAL '30 DAYS';
            """
        },
        {
            "name": "idx_current_stat_very_recent",
            "sql": """
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_current_stat_very_recent
                ON current_stat_table(uid, datetime DESC)
                WHERE datetime >= CURRENT_DATE - INTERVAL '7 DAYS';
            """
        },
        {
            "name": "idx_current_stat_datetime",
            "sql": """
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_current_stat_datetime 
                ON current_stat_table(datetime DESC);
            """
        },
        {
            "name": "idx_current_stat_covering",
            "sql": """
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_current_stat_covering 
                ON current_stat_table(uid, datetime DESC) 
                INCLUDE (video_total_num, article_total_num, likes_total_num, elec_num, follower_num, dahanghai_num);
            """
        }
    ]
    
    try:
        await initialize_pool()
        logger.info("Starting to apply current_stat_table performance indexes...")
        
        async with get_connection() as conn:
            for index in indexes:
                try:
                    logger.info(f"Creating index: {index['name']}")
                    await conn.execute(index['sql'])
                    logger.info(f"✓ Successfully created index: {index['name']}")
                except Exception as e:
                    logger.error(f"✗ Failed to create index {index['name']}: {e}")
                    
        logger.info("Finished applying current_stat_table performance indexes")
        
    except Exception as e:
        logger.error(f"Error applying indexes: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(apply_current_stat_indexes())
