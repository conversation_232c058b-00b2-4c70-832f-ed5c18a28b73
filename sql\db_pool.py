import asyncpg
import asyncio
from contextlib import asynccontextmanager
from typing import Optional
from logger import logger
from const import PGSQL_CONFIG

_pool: Optional[asyncpg.Pool] = None
_pool_lock = asyncio.Lock()
_pool_initialized = False

MIN_CONNECTIONS_DEFAULT = 10
MAX_CONNECTIONS_DEFAULT = 50

async def initialize_pool():
    global _pool, _pool_initialized, MIN_CONNECTIONS, MAX_CONNECTIONS
    if _pool_initialized:
        logger.debug("Pool already initialized.")
        return

    async with _pool_lock:
        if _pool_initialized:
            logger.debug("Pool already initialized (after lock).")
            return

        logger.info("Attempting to initialize asynchronous database connection pool...")

        if not isinstance(PGSQL_CONFIG, dict):
            logger.error(f"PGSQL_CONFIG is not a dictionary or not defined correctly. Type: {type(PGSQL_CONFIG)}. Value: {PGSQL_CONFIG}")
            _pool_initialized = False
            _pool = None
            raise TypeError("PGSQL_CONFIG must be a dictionary for database connection.")

        # Extract pool-specific configuration
        pool_config = PGSQL_CONFIG.copy()
        min_connections = pool_config.pop("min_size", MIN_CONNECTIONS_DEFAULT)
        max_connections = pool_config.pop("max_size", MAX_CONNECTIONS_DEFAULT)
        max_inactive_lifetime = pool_config.pop("max_inactive_connection_lifetime", None)
        max_queries = pool_config.pop("max_queries", None)

        logged_config = pool_config.copy()
        if 'password' in logged_config:
            logged_config['password'] = '********'
        logger.info(f"Using PGSQL_CONFIG: {logged_config} (min_size={min_connections}, max_size={max_connections})")

        # Build pool creation arguments
        pool_args = {
            **pool_config,
            "min_size": min_connections,
            "max_size": max_connections
        }

        if max_inactive_lifetime:
            pool_args["max_inactive_connection_lifetime"] = max_inactive_lifetime
        if max_queries:
            pool_args["max_queries"] = max_queries

        try:
            _pool = await asyncpg.create_pool(**pool_args)
            _pool_initialized = True
            logger.info("Asynchronous database connection pool initialized successfully.")
        except TypeError as te:
            logger.error(f"TypeError during pool creation. PGSQL_CONFIG might be malformed or missing required arguments for asyncpg.create_pool: {te}. Config was: {logged_config}")
            _pool_initialized = False
            _pool = None
            raise
        except (ConnectionError, asyncpg.exceptions.PostgresError) as conn_err:
            logger.error(f"Connection error during asyncpg.create_pool: {conn_err.__class__.__name__}: {conn_err}. Config was: {logged_config}")
            _pool_initialized = False
            _pool = None
            raise
        except Exception as e:
            logger.error(f"Failed to initialize asynchronous database connection pool with an unexpected error: {e.__class__.__name__}: {e}. Config was: {logged_config}")
            _pool_initialized = False
            _pool = None
            raise

async def shutdown_pool():
    global _pool, _pool_initialized
    if _pool_initialized:
        async with _pool_lock:
            if _pool_initialized and _pool:
                logger.info("Closing asynchronous database connection pool.")
                await _pool.close()
                _pool = None
                _pool_initialized = False
                logger.info("Asynchronous database connection pool closed.")

@asynccontextmanager
async def get_connection():
    """
    eg
    async with get_connection() as conn:
        await conn.execute("SELECT * FROM table")
    """
    if not _pool_initialized or _pool is None:
        logger.info("Pool not initialized or None in get_connection. Attempting to (re)initialize.")
        try:
            await initialize_pool()
        except Exception as e:
            logger.error(f"Error during lazy initialization of pool in get_connection: {e.__class__.__name__}: {e}")
            raise RuntimeError(f"Failed to initialize database pool during get_connection: {e}")

    if _pool is None:
        logger.error("Database pool is None even after initialization attempt in get_connection.")
        raise RuntimeError("Database pool is not available (None after init attempt).")

    conn = None
    try:
        logger.debug("Acquiring connection from pool...")
        conn = await _pool.acquire()
        logger.debug("Connection acquired successfully.")
        yield conn
    except asyncio.TimeoutError:
        logger.error("Timeout acquiring database connection from pool.")
        raise RuntimeError("Timeout acquiring database connection.")
    except Exception as e:
        logger.error(f"Error acquiring database connection from pool: {e.__class__.__name__}: {e}")
        raise RuntimeError(f"Failed to acquire database connection: {e}")
    finally:
        if conn is not None:
            try:
                logger.debug("Releasing connection back to pool.")
                await _pool.release(conn)
                logger.debug("Connection released successfully.")
            except Exception as e:
                logger.error(f"Error releasing database connection: {e.__class__.__name__}: {e}")
