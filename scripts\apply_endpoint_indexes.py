#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to apply performance indexes for the 5 optimized API endpoints
Run this script to create database indexes that will improve query performance
"""

import asyncio
import asyncpg
import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sql.db_pool import initialize_pool, get_connection, shutdown_pool
from logger import logger


async def apply_indexes():
    """Apply all performance indexes for the optimized endpoints"""
    
    # Read the SQL file with all indexes
    sql_file_path = project_root / "sql" / "endpoint_performance_indexes.sql"
    
    if not sql_file_path.exists():
        logger.error(f"SQL file not found: {sql_file_path}")
        return False
    
    with open(sql_file_path, 'r', encoding='utf-8') as f:
        sql_content = f.read()
    
    # Split into individual CREATE INDEX statements
    statements = []
    current_statement = []
    
    for line in sql_content.split('\n'):
        line = line.strip()
        
        # Skip comments and empty lines
        if not line or line.startswith('--'):
            continue
        
        current_statement.append(line)
        
        # End of statement
        if line.endswith(';'):
            statement = ' '.join(current_statement).strip()
            if statement.upper().startswith('CREATE INDEX'):
                statements.append(statement)
            current_statement = []
    
    logger.info(f"Found {len(statements)} index creation statements")
    
    try:
        async with get_connection() as conn:
            success_count = 0
            error_count = 0
            
            for i, statement in enumerate(statements, 1):
                try:
                    logger.info(f"Executing index {i}/{len(statements)}: {statement[:100]}...")
                    await conn.execute(statement)
                    success_count += 1
                    logger.info(f"✓ Index {i} created successfully")
                    
                except asyncpg.exceptions.DuplicateTableError:
                    logger.info(f"✓ Index {i} already exists, skipping")
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"✗ Failed to create index {i}: {e}")
                    error_count += 1
            
            logger.info(f"Index creation completed: {success_count} successful, {error_count} failed")
            return error_count == 0
            
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        return False


async def check_existing_indexes():
    """Check which indexes already exist"""
    try:
        async with get_connection() as conn:
            # Query to get all indexes related to our tables
            query = """
                SELECT 
                    schemaname,
                    tablename,
                    indexname,
                    indexdef
                FROM pg_indexes 
                WHERE tablename IN ('live_session_table', 'live_status_minute_table', 'dahanghai_list_table')
                AND indexname LIKE 'idx_%'
                ORDER BY tablename, indexname
            """
            
            indexes = await conn.fetch(query)
            
            if indexes:
                logger.info("Existing performance indexes:")
                for idx in indexes:
                    logger.info(f"  {idx['tablename']}.{idx['indexname']}")
            else:
                logger.info("No existing performance indexes found")
            
            return indexes
            
    except Exception as e:
        logger.error(f"Error checking existing indexes: {e}")
        return []


async def get_table_sizes():
    """Get table and index sizes for monitoring"""
    try:
        async with get_connection() as conn:
            query = """
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as table_size,
                    pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as indexes_size,
                    pg_total_relation_size(schemaname||'.'||tablename) as table_size_bytes
                FROM pg_tables 
                WHERE tablename IN ('live_session_table', 'live_status_minute_table', 'dahanghai_list_table')
                ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
            """
            
            sizes = await conn.fetch(query)
            
            if sizes:
                logger.info("Table and index sizes:")
                for size_info in sizes:
                    logger.info(f"  {size_info['tablename']}: {size_info['table_size']} (indexes: {size_info['indexes_size']})")
            
            return sizes
            
    except Exception as e:
        logger.error(f"Error getting table sizes: {e}")
        return []


async def main():
    """Main function to apply indexes and show statistics"""
    logger.info("Starting endpoint performance index application")
    
    try:
        # Initialize database pool
        await initialize_pool()
        logger.info("Database pool initialized")
        
        # Check existing indexes
        logger.info("Checking existing indexes...")
        await check_existing_indexes()
        
        # Get table sizes before
        logger.info("Getting table sizes before index creation...")
        sizes_before = await get_table_sizes()
        
        # Apply indexes
        logger.info("Applying performance indexes...")
        success = await apply_indexes()
        
        if success:
            logger.info("All indexes applied successfully!")
            
            # Get table sizes after
            logger.info("Getting table sizes after index creation...")
            await get_table_sizes()
            
            logger.info("Index application completed successfully")
            logger.info("The following endpoints should now have improved performance:")
            logger.info("  - GET /live/sessions")
            logger.info("  - GET /live/one_session")
            logger.info("  - GET /live/one_session/minute")
            logger.info("  - GET /board/dahanghai/target")
            logger.info("  - GET /board/dahanghai/target_period")
            
        else:
            logger.error("Some indexes failed to be created. Check the logs above.")
            return 1
            
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        return 1
        
    finally:
        # Shutdown database pool
        await shutdown_pool()
        logger.info("Database pool closed")
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
