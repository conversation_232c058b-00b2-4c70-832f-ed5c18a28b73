#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to apply performance indexes for the live status endpoint optimization
"""

import asyncio
import time
from sql.db_pool import get_connection
from logger import logger

async def apply_performance_indexes():
    """Apply performance indexes to optimize live status queries"""
    
    indexes = [
        {
            "name": "idx_live_status_room_datetime",
            "sql": """
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_status_room_datetime 
                ON live_status_minute_table(room_id, datetime DESC);
            """
        },
        {
            "name": "idx_user_info_room_id", 
            "sql": """
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_info_room_id 
                ON user_info_table(room_id);
            """
        },
        {
            "name": "idx_live_status_recent",
            "sql": """
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_status_recent
                ON live_status_minute_table(datetime DESC)
                WHERE datetime >= CURRENT_DATE - INTERVAL '7 DAYS';
            """
        },
        {
            "name": "idx_live_status_status_datetime",
            "sql": """
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_status_status_datetime
                ON live_status_minute_table(live_status, datetime DESC)
                WHERE datetime >= CURRENT_DATE - INTERVAL '7 DAYS';
            """
        },
        {
            "name": "idx_live_status_room_status",
            "sql": """
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_live_status_room_status 
                ON live_status_minute_table(room_id, live_status, datetime DESC);
            """
        }
    ]
    
    logger.info("Starting to apply performance indexes...")
    
    async with get_connection() as conn:
        for index in indexes:
            try:
                logger.info(f"Creating index: {index['name']}")
                start_time = time.time()
                
                await conn.execute(index['sql'])
                
                creation_time = time.time() - start_time
                logger.info(f"Index {index['name']} created successfully in {creation_time:.2f}s")
                
            except Exception as e:
                logger.error(f"Failed to create index {index['name']}: {e}")
                continue
    
    logger.info("Performance indexes application completed")

async def check_existing_indexes():
    """Check which indexes already exist"""
    
    check_sql = """
        SELECT indexname, tablename 
        FROM pg_indexes 
        WHERE tablename IN ('live_status_minute_table', 'user_info_table')
        AND indexname LIKE 'idx_%'
        ORDER BY tablename, indexname;
    """
    
    async with get_connection() as conn:
        results = await conn.fetch(check_sql)
        
        logger.info("Existing performance indexes:")
        for row in results:
            logger.info(f"  {row['tablename']}.{row['indexname']}")
        
        return results

if __name__ == "__main__":
    async def main():
        logger.info("Checking existing indexes...")
        await check_existing_indexes()
        
        logger.info("\nApplying new performance indexes...")
        await apply_performance_indexes()
        
        logger.info("\nFinal index status:")
        await check_existing_indexes()
    
    asyncio.run(main())
