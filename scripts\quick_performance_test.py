#!/usr/bin/env python3
"""
Quick performance test for the 5 optimized endpoints
"""

import requests
import time
import json

BASE_URL = "http://localhost:9022"

def test_endpoint(name, url, iterations=3):
    """Test an endpoint multiple times and return average response time"""
    print(f"\n🔍 Testing {name}")
    print(f"URL: {url}")
    
    times = []
    for i in range(iterations):
        try:
            start = time.time()
            response = requests.get(f"{BASE_URL}{url}", timeout=30)
            end = time.time()
            
            response_time = (end - start) * 1000  # Convert to milliseconds
            times.append(response_time)
            
            status = "✅" if response.status_code == 200 else "❌"
            print(f"  Attempt {i+1}: {response_time:.0f}ms {status}")
            
        except Exception as e:
            print(f"  Attempt {i+1}: ERROR - {e}")
    
    if times:
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        print(f"  📊 Average: {avg_time:.0f}ms | Min: {min_time:.0f}ms | Max: {max_time:.0f}ms")
        return avg_time
    else:
        print(f"  ❌ All requests failed")
        return None

def main():
    print("🚀 Quick Performance Test for Optimized API Endpoints")
    print("=" * 60)
    
    # Test endpoints
    endpoints = [
        ("Live Sessions", "/live/sessions?vtuber=星瞳"),
        ("Board Dahanghai Target", "/board/dahanghai/target?target=星瞳&c=2025-08-20&p=2025-08-15&n=5"),
        ("Board Dahanghai Target Period", "/board/dahanghai/target_period?target=星瞳&start_time_str1=2025-08-01&end_time_str1=2025-08-10&start_time_str2=2025-08-11&end_time_str2=2025-08-20&n=5"),
    ]
    
    results = {}
    total_time = 0
    successful_tests = 0
    
    for name, url in endpoints:
        avg_time = test_endpoint(name, url)
        if avg_time is not None:
            results[name] = avg_time
            total_time += avg_time
            successful_tests += 1
    
    # Summary
    print("\n" + "=" * 60)
    print("📈 PERFORMANCE SUMMARY")
    print("=" * 60)
    
    for name, avg_time in results.items():
        print(f"{name:30} {avg_time:8.0f}ms")
    
    if successful_tests > 0:
        overall_avg = total_time / successful_tests
        print("-" * 60)
        print(f"{'Overall Average':30} {overall_avg:8.0f}ms")
        print(f"{'Successful Tests':30} {successful_tests:8d}/{len(endpoints)}")
    
    print("\n✅ Performance test completed!")
    print("💡 Note: These are basic response time tests.")
    print("💡 For comprehensive testing, use the full performance test script.")

if __name__ == "__main__":
    main()
